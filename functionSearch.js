// Constants

const EVENT_TYPES = [
	"eventON_LOAD",
	"eventON_CHANGE",
	"eventON_INPUT",
	"eventON_BLUR",
	"eventON_FOCUS",
	"eventON_SVCERROR",
	"eventON_SVCITEMS",
	"eventON_BEFORE_SHOW_DAY"
];
const SEARCH_OPTIONS = {
	ALL: "all",
	FUNCTION_NAME: "funName",
	NOT_USED: "notUsed"
};
const SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];

/**
 * Builds a map of control labels to their bound object values for specified events
 * @param {string} viewName - The name of the view to search
 * @param {Array<string>} eventTypes - Array of event types to check for
 * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs
 */
function buildBoundObjectMap(viewName, eventTypes) {
	const view = bpmext.ui.getView(viewName);
	const boundObjectMap = new Map();
	console.dir(view);
	if (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {
		console.error("View or view children not found");
		return boundObjectMap;
	}

	const children = view._bpmextViewNode._children;
	if (!children || Object.keys(children).length === 0) {
		console.error("No children found in the view");
		return boundObjectMap;
	}
	// Iterate through all child controls
	for (const controlType in children) {
		if (Object.prototype.hasOwnProperty.call(children, controlType)) {
			const controls = children[controlType];

			// Process each control of this type
			for (const control of controls) {
				if (!control._data) continue;

				const label = control._data.getLabel ? control._data.getLabel() : controlType;

				// Check for each event type
				for (const eventType of eventTypes) {
					if (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {
						// Get the bound object value
						const value = control._data.context.options[eventType].boundObject.value;

						// Initialize array for this label if it doesn't exist
						if (!boundObjectMap.has(label)) {
							boundObjectMap.set(label, []);
						}

						// Add event type and value to the array for this label
						boundObjectMap.get(label).push({
							eventType: eventType,
							value: value,
						});
					}
				}
			}
		}
	}
	return boundObjectMap;
}

/**
 * Searches for a string in the bound object values and returns matching control labels
 * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs
 * @param {string} searchString - String to search for in bound object values
 * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events
 */
function searchBoundObjects(boundObjectMap, searchString) {
	if (!searchString || typeof searchString !== "string") {
		return [];
	}

	const matchingResults = [];

	// Convert search string to lowercase for case-insensitive search
	const searchLower = searchString.toLowerCase();

	// Search through all values in the map
	boundObjectMap.forEach((eventArray, label) => {
		// Find matching events for this control
		const matchingEvents = eventArray.filter((event) => typeof event.value === "string" && event.value.toLowerCase().includes(searchLower));

		// If we found any matches, add this control to the results
		if (matchingEvents.length > 0) {
			matchingResults.push({
				label: label,
				events: matchingEvents,
			});
		}
	});

	return matchingResults;
}

/**
 * Main function to search for bound objects in a view
 * @param {string} viewName - The name of the view to search
 * @param {string} searchString - String to search for in bound object values
 * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events
 */
function searchFunctionsInView(viewName, searchString) {
	const boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);
	return searchBoundObjects(boundObjectMap, searchString);
}

/**
 * Gets just the labels of controls that match the search string
 * @param {string} viewName - The name of the view to search
 * @param {string} searchString - String to search for in bound object values
 * @returns {Array<string>} Array of distinct control labels that match the search
 */
function getMatchingControlLabels(viewName, searchString) {
	const results = searchFunctionsInView(viewName, searchString);
	return results.map((result) => result.label);
}

/**
 * Formats search results as an HTML table
 * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView
 * @param {string} searchString - The search string used (for highlighting)
 * @returns {string} HTML table representation of the search results
 */
function formatResultsAsHtml(results, searchString) {
	if (!results || results.length === 0) {
		return "<div class='no-results'>No matching controls found</div>";
	}

	// Escape HTML special characters to prevent XSS
	function escapeHtml(text) {
		return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
	}

	// Highlight the search string in the value
	function highlightSearchString(text, search) {
		if (!search || !text.includes(search)) {
			return escapeHtml(text);
		}

		const escapedText = escapeHtml(text);
		const regex = new RegExp(escapeHtml(search), "gi");
		return escapedText.replace(regex, (match) => `<span class="highlight">${match}</span>`);
	}

	// Build the HTML table with unified design
	let html = `
    <style>
        .search-results-table {
            border-collapse: collapse;
            width: 100%;
            font-family: Arial, sans-serif;
            margin-bottom: 20px;
            table-layout: fixed;
        }
        .search-results-table th, .search-results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .search-results-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {
            width: 20%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {
            width: 20%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {
            width: 15%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {
            width: 45%;
            word-wrap: break-word;
            white-space: normal;
        }
        .search-results-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .search-results-table tr:hover {
            background-color: #f5f5f5;
        }
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }
        .event-type {
            font-style: italic;
            color: #666;
        }
        .no-results {
            padding: 20px;
            text-align: center;
            font-style: italic;
            color: #666;
        }
        .function-group {
            border-top: 2px solid #0066cc;
        }
        .controller-group {
            border-left: 3px solid #f0f0f0;
        }
    </style>
    <table class="search-results-table">
        <thead>
            <tr>
                <th>Function Name</th>
                <th>Used by Control</th>
                <th>Event Type</th>
                <th>Context</th>
            </tr>
        </thead>
        <tbody>
    `;

	// Add rows for each result
	results.forEach((result) => {
		result.events.forEach((event, index) => {
			html += `
            <tr>
                <td>${escapeHtml(searchString)}</td>
                <td>${index === 0 ? escapeHtml(result.label) : ""}</td>
                <td class="event-type">${escapeHtml(event.eventType.replace("event", ""))}</td>
                <td>${highlightSearchString(event.value, searchString)}</td>
            </tr>
            `;
		});
	});

	html += `
        </tbody>
    </table>
    `;

	return html;
}

/**
 * Checks if a function name is a system function
 * @param {string} functionName - The function name to check
 * @returns {boolean} True if it's a system function
 */
function isSystemFunction(functionName) {
	return functionName.startsWith('event') ||
		   functionName.includes('ON_') ||
		   functionName.startsWith('get') ||
		   functionName.startsWith('set') ||
		   functionName.startsWith('_');
}

/**
 * Gets all user-defined functions from a view
 * @param {Object} view - The view object
 * @returns {Array<string>} Array of user function names
 */
function getUserFunctions(view) {
	const userFunctions = [];
	const ownPropertyNames = Object.getOwnPropertyNames(view);

	for (const key of ownPropertyNames) {
		try {
			// Skip system properties
			if (SYSTEM_PROPERTIES.includes(key)) {
				continue;
			}

			const value = view[key];

			// Check if it's a user-defined function
			if (typeof value === 'function' && !isSystemFunction(key)) {
				userFunctions.push(key);
			}
		} catch (e) {
			console.log(`Error accessing property ${key}: ${e.message}`);
		}
	}

	return userFunctions;
}

/**
 * Extracts all user-defined function names from a view and finds their usage in controls
 * @param {string} viewName - The name of the view to extract functions from
 * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view
 */
function extractFunctionNamesFromView(viewName) {
	const view = bpmext.ui.getView(viewName);
	const functionDetails = [];

	if (!view) {
		console.error("View not found");
		return functionDetails;
	}

	console.dir(view); // Debug logging

	// Get all user-defined functions
	const userFunctions = getUserFunctions(view);
	const boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);

	// Find usage for each function
	userFunctions.forEach(functionName => {
		const usageResults = searchBoundObjects(boundObjectMap, functionName);

		if (usageResults.length > 0) {
			// Function is used in controls
			usageResults.forEach(result => {
				result.events.forEach(event => {
					functionDetails.push({
						name: functionName,
						controller: result.label,
						eventType: event.eventType.replace("event", ""),
						context: event.value || functionName
					});
				});
			});
		} else {
			// Function exists but not used
			functionDetails.push({
				name: functionName,
				controller: 'Not Used',
				eventType: 'Available',
				context: 'Function not bound to any control'
			});
		}
	});

	return functionDetails;
}

/**
 * Filters function details to show only unused functions
 * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details
 * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details
 */
function getNotUsedFunctions(functionDetails) {
	return functionDetails.filter(detail => detail.controller === 'Not Used');
}

/**
 * Formats function details as an HTML table with grouping
 * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details
 * @returns {string} HTML table representation of the function details
 */
function formatFunctionNamesAsHtml(functionDetails) {
	if (!functionDetails || functionDetails.length === 0) {
		return "<div class='no-results'>No functions found in the view</div>";
	}

	// Escape HTML special characters to prevent XSS
	function escapeHtml(text) {
		return String(text).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
	}

	// Group by function name first, then by controller
	const groupedByFunction = {};
	functionDetails.forEach(detail => {
		if (!groupedByFunction[detail.name]) {
			groupedByFunction[detail.name] = {};
		}
		if (!groupedByFunction[detail.name][detail.controller]) {
			groupedByFunction[detail.name][detail.controller] = [];
		}
		groupedByFunction[detail.name][detail.controller].push({
			eventType: detail.eventType,
			context: detail.context
		});
	});

	// Sort function names alphabetically
	const sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>
		a.toLowerCase().localeCompare(b.toLowerCase())
	);

	// Highlight function name in context
	function highlightFunctionInContext(text, functionName) {
		if (!functionName || !text.includes(functionName)) {
			return escapeHtml(text);
		}

		const escapedText = escapeHtml(text);
		const regex = new RegExp(escapeHtml(functionName), "gi");
		return escapedText.replace(regex, (match) => `<span class="highlight">${match}</span>`);
	}

	// Build the HTML table with simple, clean design
	let html = `
    <style>
        .search-results-table {
            border-collapse: collapse;
            width: 100%;
            font-family: Arial, sans-serif;
            margin-bottom: 20px;
            table-layout: fixed;
        }
        .search-results-table th, .search-results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .search-results-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {
            width: 20%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {
            width: 20%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {
            width: 15%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {
            width: 45%;
            word-wrap: break-word;
            white-space: normal;
        }
        .search-results-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .search-results-table tr:hover {
            background-color: #f5f5f5;
        }
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }
        .event-type {
            font-style: italic;
            color: #666;
        }
        .no-results {
            padding: 20px;
            text-align: center;
            font-style: italic;
            color: #666;
        }
        .function-group {
            border-top: 2px solid #0066cc;
        }
        .controller-group {
            border-left: 3px solid #f0f0f0;
        }
    </style>
    <table class="search-results-table">
        <thead>
            <tr>
                <th>Function Name</th>
                <th>Used by Control</th>
                <th>Event Type</th>
                <th>Context</th>
            </tr>
        </thead>
        <tbody>
    `;

	// Add rows for each function group
	sortedFunctionNames.forEach((functionName) => {
		const controllers = groupedByFunction[functionName];
		const sortedControllers = Object.keys(controllers).sort((a, b) =>
			a.toLowerCase().localeCompare(b.toLowerCase())
		);

		let isFirstRowForFunction = true;

		sortedControllers.forEach((controller) => {
			const eventDetails = controllers[controller];
			let isFirstRowForController = true;

			eventDetails.forEach((detail) => {
				const functionGroupClass = isFirstRowForFunction ? 'function-group' : '';
				const controllerGroupClass = isFirstRowForController ? 'controller-group' : '';

				html += `
                <tr class="${functionGroupClass}">
                    <td class="${controllerGroupClass}">
                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}
                    </td>
                    <td class="${controllerGroupClass}">
                        ${isFirstRowForController ? escapeHtml(controller) : ''}
                    </td>
                    <td class="event-type">${escapeHtml(detail.eventType)}</td>
                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>
                </tr>
                `;

				isFirstRowForFunction = false;
				isFirstRowForController = false;
			});
		});
	});

	html += `
        </tbody>
    </table>
    `;

	return html;
}

/**
 * Searches for functions in a view and returns the results as HTML
 * @param {string} viewName - The name of the view to search
 * @param {string} searchFor - Search option (all, funName, notUsed)
 * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)
 * @returns {string} HTML representation of the search results
 */
function searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {
	switch (searchFor) {
		case SEARCH_OPTIONS.ALL:
			// Show all user-defined functions
			const allFunctionDetails = extractFunctionNamesFromView(viewName);
			return formatFunctionNamesAsHtml(allFunctionDetails);

		case SEARCH_OPTIONS.FUNCTION_NAME:
			// Search for specific function name
			if (!searchString) {
				return "<div class='no-results'>Please enter a function name to search for</div>";
			}
			const results = searchFunctionsInView(viewName, searchString);
			return formatResultsAsHtml(results, searchString);

		case SEARCH_OPTIONS.NOT_USED:
			// Show only unused functions
			const allFunctions = extractFunctionNamesFromView(viewName);
			const notUsedFunctions = getNotUsedFunctions(allFunctions);
			return formatFunctionNamesAsHtml(notUsedFunctions);

		default:
			// Default to showing all functions
			const defaultFunctionDetails = extractFunctionNamesFromView(viewName);
			return formatFunctionNamesAsHtml(defaultFunctionDetails);
	}
}

this.executeSearch = function () {
	var viewName = this.ui.get("viewName").getData();
	if (!viewName) {
		console.error("View name is empty");
		return;
	}

	var searchForVal = this.ui.get("searchFor").getData();
	var funName = this.ui.get("functionName").getData();

	console.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);

	var htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);
	if (!!htmlResults) {
		this.ui.get("result").setData(htmlResults);
	}
};
